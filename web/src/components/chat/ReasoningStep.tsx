import React, { useState } from 'react';
import { cn } from '@/lib/utils/css';
import { ChevronDown } from 'lucide-react';

export interface ReasoningStepProps {
  content: string;
  defaultCollapsed?: boolean;
}

const ReasoningStep: React.FC<ReasoningStepProps> = ({
  content,
  defaultCollapsed = true
}) => {
  const [isCollapsed, setIsCollapsed] = useState(defaultCollapsed);

  const toggleCollapsed = () => {
    setIsCollapsed(!isCollapsed);
  };

  return (
    <div>
      <div
        className="flex items-center cursor-pointer hover:opacity-80 transition-opacity"
        onClick={toggleCollapsed}
      >
        <ChevronDown
          style={{
            width: '12px',
            height: '12px',
            marginRight: '4px',
            color: 'rgba(115, 122, 135, 0.6)',
          }}
          className={cn('transition-transform', isCollapsed && 'rotate-180')}
        />
        <span className="text-[#888B94] text-[12px] font-medium user-select-none">Mobile Use 思考中...</span>
      </div>
      {!isCollapsed && (
        <div className="text-[#888B94] text-[13px] ml-4 leading-relaxed">
          {content}
        </div>
      )}
    </div>
  );
};

export default ReasoningStep;
