from pydantic import BaseModel, Field
from typing import Optional
import os


class BatchExecutionConfig(BaseModel):
    """简化的批量执行配置"""
    
    # 基本执行参数
    csv_file_path: str = Field(default="cagui.csv", description="CSV文件路径")
    start_index: int = Field(default=1, description="起始索引（从1开始）")
    end_index: Optional[int] = Field(default=None, description="结束索引（包含，None表示到末尾）")
    
    # 执行控制
    delay_between_instructions: float = Field(default=2.0, description="指令间延迟（秒）")
    timeout_per_instruction: int = Field(default=120, description="单个指令超时时间（秒）")
    max_retries: int = Field(default=1, description="最大重试次数")
    
    # 输出配置
    result_output_dir: str = Field(default="batch_results", description="结果输出目录")
    save_screenshots: bool = Field(default=False, description="是否保存截图")
    
    # 调试选项
    mock_mode: bool = Field(default=False, description="模拟模式（不实际执行）")
    verbose: bool = Field(default=True, description="详细输出")
    
    # 环境配置
    csv_encoding: str = Field(default="utf-8", description="CSV文件编码")
    
    @classmethod
    def from_env(cls) -> "BatchExecutionConfig":
        """从环境变量创建配置"""
        return cls(
            csv_file_path=os.getenv("BATCH_CSV_FILE", "cagui.csv"),
            start_index=int(os.getenv("BATCH_START_INDEX", "1")),
            end_index=int(os.getenv("BATCH_END_INDEX")) if os.getenv("BATCH_END_INDEX") else None,
            delay_between_instructions=float(os.getenv("BATCH_DELAY", "2.0")),
            timeout_per_instruction=int(os.getenv("BATCH_TIMEOUT", "120")),
            max_retries=int(os.getenv("BATCH_MAX_RETRIES", "1")),
            result_output_dir=os.getenv("BATCH_OUTPUT_DIR", "batch_results"),
            mock_mode=os.getenv("BATCH_MOCK_MODE", "false").lower() == "true",
            verbose=os.getenv("BATCH_VERBOSE", "true").lower() == "true",
        )
    
    def get_csv_path(self, base_dir: str = ".") -> str:
        """获取CSV文件的完整路径"""
        import os
        if os.path.isabs(self.csv_file_path):
            return self.csv_file_path
        return os.path.join(base_dir, self.csv_file_path)
    
    def get_output_dir(self, base_dir: str = ".") -> str:
        """获取输出目录的完整路径"""
        import os
        if os.path.isabs(self.result_output_dir):
            return self.result_output_dir
        return os.path.join(base_dir, self.result_output_dir)
