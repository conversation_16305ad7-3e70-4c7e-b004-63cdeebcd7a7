from datetime import datetime
from langchain_core.messages import BaseMessage, AIMessage, HumanMessage, SystemMessage
from langchain_deepseek import ChatDeepSeek
from openai.types.chat import (
    ChatCompletionContentPartImageParam,
    ChatCompletionContentPartTextParam,
)
from openai.types.chat.chat_completion_content_part_image_param import ImageURL
from mobile_agent.agent.memory.messages import AgentMessages
from mobile_agent.agent.llm.doubao import DoubaoLLM
from mobile_agent.agent.prompt.summary import summary_system_prompt
import logging

logger = logging.getLogger(__name__)


class ContextManager:
    def __init__(self, messages: list[BaseMessage]):
        self._messages = AgentMessages(messages)

    def _append(self, message: BaseMessage):
        self._messages.append(message)

    def get_messages(self):
        return self._messages.get_messages()

    def length(self):
        return self._messages.length()

    def add_system_message(self, message: str):
        """系统消息"""
        system_message = SystemMessage(content=message)
        if self._messages.length() > 0 and self._messages.index(0).type == "system":
            self._messages.replace(0, system_message)
        else:
            self._messages.insert(0, system_message)

    def add_user_initial_message(
        self,
        user_prompt: str,
        screenshot_url: str,
        app_list: str,
    ):
        """初始消息"""
        user_content = f"""
当前时间点 {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
手机初始化APP列表信息:
```
{app_list}
```
用户对你说: {user_prompt}
"""
        self._append(
            ContextManager.get_snapshot_user_prompt(
                url=screenshot_url, user_content=user_content
            )
        )

    async def add_user_iteration_message(
        self,
        user_prompt: str,
        iteration_count: int,
        tool_output: str,
        screenshot_url: str,
        screenshot_dimensions: tuple,
    ):
        """
        ReAct 迭代中的消息
        """
        # 如果需要总结，则走总结逻辑
        summary_message = await self.summary()
        if summary_message:
            # 只要保留前2条消息， 因为前2条消息是系统消息、初始任务提示消息
            initial_user_message = self._messages.get_messages()[1]
            initial_user_message_content = self._get_message_text_content(
                initial_user_message
            )
            # 只拿系统消息
            self._messages.replace_all(self._messages.get_messages()[:1])
            user_content = f"""
---
初始用户提示:
{initial_user_message_content}
---
当前已经迭代了许多次了 {iteration_count}
我做了的工作总结如下:
```
{summary_message}
```
请检查是否还需要完成用户的任务
```
{user_prompt}
```
如果需要继续执行，否则找用户确认是否需要继续执行

当前轮次工具下发结果:
```
{tool_output}
```
请观察截图， 当前截图分辨率为 {screenshot_dimensions[0]}x{screenshot_dimensions[1]}"""
        else:
            user_content = f"""
当前轮次迭代次数 {iteration_count}
用户任务: {user_prompt}
当前轮次工具下发结果:
```
{tool_output}
```
请观察截图， 当前截图分辨率为 {screenshot_dimensions[0]}x{screenshot_dimensions[1]}"""
        self._append(
            ContextManager.get_snapshot_user_prompt(
                url=screenshot_url, user_content=user_content
            )
        )

    def add_ai_message(self, content: str):
        """添加AI消息"""
        self._append(AIMessage(role="assistant", content=content))

    def _get_message_text_content(self, message: BaseMessage) -> str:
        """获取消息的文本内容"""
        if isinstance(message.content, list):
            return "\n".join(
                [
                    part.get("text", "")
                    for part in message.content
                    if part.get("type") == "text"
                ]
            )
        else:
            return str(message.content)

    def keep_last_n_images_in_messages(self, keep_n: int):
        """保留最后n张图片URL"""
        # 收集所有图片URL及其信息
        all_image_parts = []
        messages = self._messages.get_messages()
        for msg in messages:
            if msg.type == "human" and isinstance(msg.content, list):
                for i, part in enumerate(msg.content):
                    if isinstance(part, dict) and part.get("type") == "image_url":
                        # 保存消息和图片在消息中的索引
                        all_image_parts.append((msg, part, i))
        # 计算需要保留的图片数量
        total_images = len(all_image_parts)
        # 如果图片总数小于等于keep_n，不需要删除
        if total_images <= keep_n:
            return
        # 计算需要删除的图片数量
        to_remove_count = total_images - keep_n
        # 删除旧的图片URL（从前往后删除）
        for i in range(to_remove_count):
            msg, part, _ = all_image_parts[i]
            if part in msg.content:
                msg.content.remove(part)

        self._messages.replace_all(messages)

    @staticmethod
    def get_snapshot_user_prompt(url: str, user_content: str) -> HumanMessage:
        # 添加截图消息
        snap_content = ChatCompletionContentPartImageParam(
            image_url=ImageURL(url=url), type="image_url"
        )
        #  UserPrompt
        user_message = ChatCompletionContentPartTextParam(
            text=user_content, type="text"
        )
        screenshot_message = HumanMessage(
            role="user", content=[snap_content, user_message]
        )
        return screenshot_message

    async def summary(self) -> str | None:
        messages = self._messages.get_messages()

        # 如果消息数量不足5条，不需要总结
        if len(messages) <= 5:
            return

        # 跳过前三条消息，总结消息
        # SystemMessage \ HumanMessage \ AIMessage - [NeedSummary] - ([HumanMessageWithImage]\[AIMessage])
        # 总结后
        # SystemMessage \ HumanMessage \ AIMessage - [HumanSummaryMessage]

        # 确定需要总结的消息范围：从第4条开始到最后一对截屏消息之前
        messages_to_summarize = messages[3:]

        # 如果没有需要总结的消息，返回
        if not messages_to_summarize or len(messages_to_summarize) < 2:
            return

        # 将需要总结的消息转换为文本
        text_to_summarize = "\n\n".join(
            f"{msg.type}: {self._get_message_text_content(msg)}"
            for msg in messages_to_summarize
            if self._get_message_text_content(msg)
        )
        # 检查是否超出token限制
        if (
            len(text_to_summarize)
            < DoubaoLLM.max_input_context * DoubaoLLM.context_ratio
        ):
            # 天空为什么这么蓝 => 4 个token
            # 比如我最大的窗口是 4个 token ，如果我文字都没有超过 4个字符串，基本也不会超过 4个 token ，所以直接返回
            return

        is_exceed = await DoubaoLLM.is_token_exceed(text_to_summarize)

        # 只有在超出token限制时才进行总结
        if not is_exceed:
            return

        llm = ChatDeepSeek(
            api_key=DoubaoLLM.api_key,
            api_base=DoubaoLLM.base_url,
            model_name=DoubaoLLM.model_name,
            temperature=DoubaoLLM.temperature,
        )
        try:
            response = await llm.ainvoke(
                [
                    SystemMessage(content=summary_system_prompt),
                    HumanMessage(
                        content=f"""
下面是已经对话后的信息，请根据系统提示词的指示帮我总结一下我们干了什么:
````
{text_to_summarize}
```
"""
                    ),
                ]
            )
            return response.content
        except Exception as e:
            logger.error(f"Summary failed: {e}")
            return None
