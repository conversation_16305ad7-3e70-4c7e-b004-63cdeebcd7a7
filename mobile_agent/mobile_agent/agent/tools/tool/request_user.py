from mobile_agent.agent.infra.message_web import (
    UserInterruptMessageData,
)
from mobile_agent.agent.tools.tool.abc import SpecialTool
from langgraph.types import interrupt


class RequestUserTool(SpecialTool):
    def __init__(self):
        super().__init__(
            name="request_user",
            description="When the task is unsolvable or you need the user's help like login, input verification code or need more information, call the user. You must exactly describe the user request in content.",
            parameters={
                "content": {
                    "type": "string",
                    "description": "The content to call the user",
                }
            },
        )

    async def handler(self, args: dict):
        request_user_content = args.get("content")
        user_response = interrupt({"request_user": request_user_content})
        query = user_response.get("query", "")
        return f"用户回复: {query}"

    def special_message(self, content: str, args: dict):
        return UserInterruptMessageData(
            id=args.get("chunk_id"),
            task_id=args.get("task_id"),
            role="assistant",
            type="user_interrupt",
            interrupt_type="text",
            content=content,
        )

    def special_memory(self, content: str = ""):
        return content
