from mobile_agent.config.settings import MOBILE_USE_MCP_NAME
from mobile_agent.agent.tools.tool.mcp_tool import Mc<PERSON><PERSON>ool
from mobile_agent.agent.infra.model import ToolCall
from mobile_agent.agent.tools.tool import (
    FinishedTool,
    WaitTool,
    RequestUserTool,
    ErrorTool,
)
from mobile_agent.agent.tools.mcp import MC<PERSON>Hub
from mobile_agent.agent.tools.tool.abc import Tool, SpecialTool
from typing import Optional


class Tools:
    def __init__(
        self,
        tools: list[Tool | SpecialTool | McpTool],
    ):
        self.tools = tools

    @classmethod
    async def from_mcp(cls, mcp_hub: MCPHub):
        tools = [
            FinishedTool(),
            WaitTool(),
            RequestUserTool(),
            ErrorTool(),
            *list(
                map(
                    lambda tool: McpTool(mcp_hub, MOBILE_USE_MCP_NAME, tool),
                    await mcp_hub.get_tools(MOBILE_USE_MCP_NAME),
                )
            ),
        ]
        return cls(tools)

    def prompt_tools(self):
        return list(map(lambda tool: tool, self.tools))

    def list_tools_schema_for_openai(self):
        tools = list(map(lambda tool: tool.get_tool_schema_for_openai(), self.tools))
        return tools

    def list_tools_prompt_string(self):
        tools = list(map(lambda tool: tool.get_prompt_string(), self.tools))
        return tools

    async def exec(self, tool_call: ToolCall):
        tool = self.get_tool_by_name(tool_call["name"])
        if tool:
            content = await tool.call(tool_call["arguments"])
            return content
        else:
            raise ValueError(f"Tool with name {tool_call['name']} not found")

    def is_special_tool(self, tool_name: str):
        tool = self.get_tool_by_name(tool_name)
        return tool and tool.is_special_tool

    def get_special_message(self, tool_name: str, content: str, args: dict):
        tool = self.get_tool_by_name(tool_name)
        if tool and tool.is_special_tool:
            return tool.special_message(content, args)
        else:
            return None

    def get_special_memory(self, tool_name: str, content: Optional[str] = None):
        tool = self.get_tool_by_name(tool_name)
        if tool and tool.is_special_tool:
            return tool.special_memory(content)
        else:
            return None

    def get_tool_by_name(self, tool_name: str) -> Tool | SpecialTool | McpTool | None:
        return next((tool for tool in self.tools if tool.name == tool_name), None)
