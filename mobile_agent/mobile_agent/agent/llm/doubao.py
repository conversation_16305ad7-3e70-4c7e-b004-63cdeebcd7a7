from typing import List, Optional
from langchain_core.messages import BaseMessage
import asyncio
import logging
from mobile_agent.config.settings import get_agent_config, get_model_config
from mobile_agent.agent.memory.messages import AgentMessages
from mobile_agent.agent.prompt.doubao_vision_pro import doubao_system_prompt
from mobile_agent.agent.llm.stream_pipe import stream_pipeline

from langchain_deepseek import ChatDeepSeek
from openai import OpenAI
from volcenginesdkarkruntime import Ark


class DoubaoLLM:
    prompt = doubao_system_prompt

    _config_initialized = False
    model_name = None
    base_url = None
    api_key = None
    temperature = None

    max_input_context = 96000  # 128k
    context_ratio = 0.80
    ark_client = None

    @classmethod
    def _initialize_config(cls):
        """初始化配置 - 只在第一次使用时执行"""
        if not cls._config_initialized:
            agent_config = get_agent_config("mobile_use")
            if agent_config.modelKey:
                model_config = get_model_config(agent_config.modelKey)
                cls.model_name = model_config.model
                cls.base_url = model_config.base_url
                cls.api_key = model_config.api_key
                cls.temperature = model_config.temperature or cls.temperature
                cls.ark_client = Ark(api_key=cls.api_key)
            cls._config_initialized = True

    def __init__(self, thread_id: Optional[str] = None, is_stream: bool = False):
        self.logger = logging.getLogger(self.__class__.__name__)
        self.is_stream = is_stream
        self.thread_id = thread_id

        # 确保配置已初始化
        self._initialize_config()

        # self.llm = ChatOpenAI(
        #     api_key=self.api_key,
        #     base_url=self.base_url,
        #     model=self.model_name,
        #     streaming=is_stream,
        #     temperature=self.temperature,
        #     stream_usage=True,
        # )
        self.llm = ChatDeepSeek(
            api_key=self.api_key,
            api_base=self.base_url,
            model_name=self.model_name,
            # streaming=is_stream,
            temperature=self.temperature,
        )

    async def async_chat(
        self, messages: List[BaseMessage]
    ) -> tuple[str, str, str, str]:
        """调用模型并处理重试逻辑"""
        max_retries = 3
        retry_count = 0

        while retry_count < max_retries:
            try:
                chunk_id, content, summary, tool_call = await self._invoke_model(
                    messages
                )

                return chunk_id, content, summary, tool_call

            except asyncio.CancelledError as e:
                # cancel 不处理，直接向外抛出
                raise e

            except Exception as e:
                retry_count += 1
                self.logger.error(f"模型调用失败，重试第 {retry_count} 次。错误: {e}")

                if retry_count >= max_retries:
                    raise e

                await asyncio.sleep(1)

    async def _invoke_model(self, messages: List[BaseMessage]) -> tuple[str, str, str]:
        if self.is_stream:
            return await self._invoke_stream_model(messages)
        else:
            return await self._invoke_sync_model(messages)

    async def _invoke_stream_model(
        self, messages: List[BaseMessage]
    ) -> tuple[str, str, str]:
        response = await self.llm.ainvoke(messages)
        # Langgraph 会监听 langchain invoke 的内容，在 graph.astream 外处理 pipe
        content, summary, tool_call = stream_pipeline.complete(id=response.id)

        return response.id, content, summary, tool_call

    async def _invoke_sync_model(
        self, messages: List[BaseMessage]
    ) -> tuple[str, str, str]:
        use_openai_client = False
        # Ark Api 在 Langchain 下拿不到 token usage
        if use_openai_client:
            client = OpenAI(api_key=self.api_key, base_url=self.base_url)
            response = client.chat.completions.create(
                model=self.model_name,
                messages=AgentMessages.convert_langchain_to_openai_messages(messages),
                temperature=self.temperature,
            )
            content = response.choices[0].message.content
            # output_tokens = response.usage.completion_tokens
            # input_tokens = response.usage.prompt_tokens

        else:
            response = await self.llm.ainvoke(messages)
            content = response.content
            # reasoning_content = response.additional_kwargs.get("reasoning_content", "")
        stream_pipeline.pipe(id=response.id, delta=content)
        content, summary, tool_call = stream_pipeline.complete(id=response.id)

        return response.id, content, summary, tool_call

    @classmethod
    async def token_count(cls, text: str) -> int:
        if cls.ark_client is None:
            cls._initialize_config()
        resp = cls.ark_client.tokenization.create(
            model=cls.model_name,
            text=[text],
        )
        if len(resp.data) > 0 and isinstance(resp.data[0].total_tokens, (int, float)):
            return resp.data[0].total_tokens
        return len(text)

    @classmethod
    async def is_token_exceed(cls, text: str) -> bool:
        if cls.ark_client is None:
            cls._initialize_config()
        token = await cls.token_count(text)
        return token > cls.max_input_context * cls.context_ratio
