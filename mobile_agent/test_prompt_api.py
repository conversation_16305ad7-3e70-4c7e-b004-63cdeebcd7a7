#!/usr/bin/env python3
"""
测试视频上传和分析API的简单脚本
"""

import asyncio
import json
from fastapi.testclient import TestClient
from mobile_agent.routers.prompt import router
from fastapi import FastAPI

# 创建测试应用
app = FastAPI()
app.include_router(router)

client = TestClient(app)

def test_video_analysis():
    """测试视频分析接口"""
    print("🧪 测试视频分析接口...")
    
    # 测试数据
    test_data = {
        "video_url": "https://example-tos-bucket.tos.region.com/test-video.mp4"
    }
    
    # 发送请求
    response = client.post("/mobile-use/api/v1/agent/analyze/video", json=test_data)
    
    print(f"状态码: {response.status_code}")
    print(f"响应内容: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")
    
    assert response.status_code == 200
    result = response.json()
    assert result["success"] == True
    assert "prompt" in result
    assert result["video_url"] == test_data["video_url"]
    
    print("✅ 视频分析接口测试通过!")

def test_video_upload():
    """测试视频上传接口"""
    print("🧪 测试视频上传接口...")
    
    # 创建模拟视频文件
    video_content = b"fake video content for testing"
    
    # 发送请求
    response = client.post(
        "/mobile-use/api/v1/agent/upload/video",
        files={"video": ("test.mp4", video_content, "video/mp4")},
        headers={"X-Top-Account-Id": "test-account-123"}
    )
    
    print(f"状态码: {response.status_code}")
    print(f"响应内容: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")
    
    assert response.status_code == 200
    result = response.json()
    assert result["success"] == True
    assert "video_url" in result
    assert result["video_size"] == len(video_content)
    
    print("✅ 视频上传接口测试通过!")

if __name__ == "__main__":
    print("🚀 开始测试视频处理API...")
    
    try:
        test_video_analysis()
        print()
        test_video_upload()
        print()
        print("🎉 所有测试通过!")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        raise
