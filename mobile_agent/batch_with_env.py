#!/usr/bin/env python3
"""
支持环境变量的批量执行脚本
可以从 .env.batch 文件或环境变量中读取配置
"""

import os
import sys
from pathlib import Path
import asyncio
import argparse
import logging

# 尝试加载 python-dotenv
try:
    from dotenv import load_dotenv
    DOTENV_AVAILABLE = True
except ImportError:
    DOTENV_AVAILABLE = False
    print("提示: 安装 python-dotenv 可以支持 .env 文件: pip install python-dotenv")

# 导入批量执行器
from batch_runner import SimpleBatchRunner

logger = logging.getLogger(__name__)


def load_env_config():
    """加载环境变量配置"""
    # 尝试加载 .env.batch 文件
    env_file = Path(".env.batch")
    if DOTENV_AVAILABLE and env_file.exists():
        load_dotenv(env_file)
        print(f"已加载配置文件: {env_file}")
    
    # 从环境变量读取配置
    config = {
        'pod_id': os.getenv('BATCH_POD_ID'),
        'product_id': os.getenv('BATCH_PRODUCT_ID'),
        'csv_file': os.getenv('BATCH_CSV_FILE', 'cagui.csv'),
        'start_index': int(os.getenv('BATCH_START_INDEX', '1')),
        'end_index': int(os.getenv('BATCH_END_INDEX')) if os.getenv('BATCH_END_INDEX') else None,
        'delay': float(os.getenv('BATCH_DELAY', '2.0')),
        'timeout': int(os.getenv('BATCH_TIMEOUT', '120')),
        'agent_url': os.getenv('BATCH_AGENT_URL', 'http://localhost:8000'),
        'output_dir': os.getenv('BATCH_OUTPUT_DIR', 'batch_results'),
        'verbose': os.getenv('BATCH_VERBOSE', 'true').lower() == 'true'
    }
    
    return config


def validate_config(config):
    """验证配置"""
    errors = []
    
    if not config['pod_id']:
        errors.append("BATCH_POD_ID 未设置")
    
    if not config['product_id']:
        errors.append("BATCH_PRODUCT_ID 未设置")
    
    if not Path(config['csv_file']).exists():
        errors.append(f"CSV 文件不存在: {config['csv_file']}")
    
    if errors:
        print("配置错误:")
        for error in errors:
            print(f"  - {error}")
        print("\n请检查环境变量或 .env.batch 文件")
        return False
    
    return True


def print_config(config):
    """打印配置信息"""
    print("批量执行配置:")
    print(f"  Pod ID: {config['pod_id']}")
    print(f"  Product ID: {config['product_id']}")
    print(f"  CSV 文件: {config['csv_file']}")
    print(f"  执行范围: {config['start_index']} - {config['end_index'] or '末尾'}")
    print(f"  指令延迟: {config['delay']}秒")
    print(f"  超时时间: {config['timeout']}秒")
    print(f"  服务地址: {config['agent_url']}")
    print(f"  输出目录: {config['output_dir']}")
    print()


async def main():
    parser = argparse.ArgumentParser(description="Mobile Agent 批量执行器 (支持环境变量)")
    parser.add_argument("--config", action="store_true", help="显示当前配置并退出")
    parser.add_argument("--dry-run", action="store_true", help="仅显示配置，不执行")
    parser.add_argument("--start", type=int, help="覆盖起始索引")
    parser.add_argument("--end", type=int, help="覆盖结束索引")
    parser.add_argument("--delay", type=float, help="覆盖指令延迟")
    
    args = parser.parse_args()
    
    # 加载配置
    config = load_env_config()
    
    # 命令行参数覆盖
    if args.start:
        config['start_index'] = args.start
    if args.end:
        config['end_index'] = args.end
    if args.delay:
        config['delay'] = args.delay
    
    # 设置日志级别
    log_level = logging.DEBUG if config['verbose'] else logging.INFO
    logging.basicConfig(
        level=log_level,
        format="%(asctime)s - %(levelname)s - %(message)s"
    )
    
    # 显示配置
    print_config(config)
    
    if args.config:
        return
    
    # 验证配置
    if not validate_config(config):
        sys.exit(1)
    
    if args.dry_run:
        print("干运行模式，不执行实际任务")
        return
    
    # 确认执行
    try:
        response = input("确认开始批量执行? (y/N): ")
        if response.lower() != 'y':
            print("用户取消执行")
            return
    except KeyboardInterrupt:
        print("\n用户取消执行")
        return
    
    # 创建输出目录
    Path(config['output_dir']).mkdir(exist_ok=True)
    
    # 创建批量执行器
    runner = SimpleBatchRunner(
        agent_base_url=config['agent_url'],
        pod_id=config['pod_id'],
        product_id=config['product_id'],
        timeout=config['timeout'],
        delay=config['delay']
    )
    
    # 运行批量执行
    try:
        await runner.run_batch(
            config['csv_file'], 
            config['start_index'], 
            config['end_index']
        )
    except KeyboardInterrupt:
        logger.info("用户中断执行")
    except Exception as e:
        logger.error(f"批量执行失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
