# Mobile Agent 批量执行环境变量配置示例
# 复制此文件为 .env.batch 并填入您的实际值

# 云手机配置 (必需)
BATCH_POD_ID=your-pod-id-here
BATCH_PRODUCT_ID=your-product-id-here

# 批量执行配置 (可选)
BATCH_CSV_FILE=cagui.csv
BATCH_START_INDEX=1
BATCH_END_INDEX=
BATCH_DELAY=2.0
BATCH_TIMEOUT=120
BATCH_AGENT_URL=http://localhost:8000

# 输出配置
BATCH_OUTPUT_DIR=batch_results
BATCH_VERBOSE=true

# 示例配置说明:
# BATCH_POD_ID: 云手机实例ID，从云手机管理控制台获取
# BATCH_PRODUCT_ID: 产品ID，从云手机管理控制台获取
# BATCH_CSV_FILE: CSV文件路径，相对于当前目录
# BATCH_START_INDEX: 起始指令索引，从1开始
# BATCH_END_INDEX: 结束指令索引，留空表示执行到最后
# BATCH_DELAY: 指令间延迟时间(秒)，避免执行过快
# BATCH_TIMEOUT: 单个指令超时时间(秒)
# BATCH_AGENT_URL: Mobile Agent 服务地址
# BATCH_OUTPUT_DIR: 结果输出目录
# BATCH_VERBOSE: 是否显示详细日志
