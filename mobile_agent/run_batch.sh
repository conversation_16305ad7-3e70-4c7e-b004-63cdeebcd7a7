#!/bin/bash

# Mobile Agent 批量执行快速启动脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示使用说明
show_usage() {
    echo "Mobile Agent 批量执行快速启动脚本"
    echo ""
    echo "使用方法:"
    echo "  $0 --pod-id POD_ID --product-id PRODUCT_ID [选项]"
    echo ""
    echo "必需参数:"
    echo "  --pod-id POD_ID        云手机实例ID"
    echo "  --product-id PRODUCT_ID 产品ID"
    echo ""
    echo "可选参数:"
    echo "  --start NUM            起始指令索引 (默认: 1)"
    echo "  --end NUM              结束指令索引 (默认: 全部)"
    echo "  --csv FILE             CSV文件路径 (默认: cagui.csv)"
    echo "  --delay SECONDS        指令间延迟 (默认: 2.0)"
    echo "  --timeout SECONDS      单个指令超时 (默认: 120)"
    echo "  --agent-url URL        Agent服务地址 (默认: http://localhost:8000)"
    echo "  --help                 显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  # 执行前3条指令"
    echo "  $0 --pod-id abc123 --product-id def456 --start 1 --end 3"
    echo ""
    echo "  # 执行所有QQ音乐指令 (1-14)"
    echo "  $0 --pod-id abc123 --product-id def456 --start 1 --end 14"
    echo ""
    echo "  # 使用自定义延迟"
    echo "  $0 --pod-id abc123 --product-id def456 --delay 5.0"
}

# 检查依赖
check_dependencies() {
    print_info "检查依赖..."
    
    # 检查 Python
    if ! command -v python3 &> /dev/null; then
        print_error "Python3 未安装"
        exit 1
    fi
    
    # 检查批量执行脚本
    if [ ! -f "batch_runner.py" ]; then
        print_error "batch_runner.py 不存在"
        exit 1
    fi
    
    # 检查 CSV 文件
    if [ ! -f "${CSV_FILE}" ]; then
        print_error "CSV 文件不存在: ${CSV_FILE}"
        exit 1
    fi
    
    print_success "依赖检查通过"
}

# 检查服务状态
check_service() {
    print_info "检查 Mobile Agent 服务状态..."
    
    if curl -s "${AGENT_URL}/docs" > /dev/null 2>&1; then
        print_success "Mobile Agent 服务运行正常"
    else
        print_warning "无法连接到 Mobile Agent 服务: ${AGENT_URL}"
        print_info "请确保服务已启动: uv run main.py"
        read -p "是否继续执行? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            exit 1
        fi
    fi
}

# 显示执行信息
show_execution_info() {
    echo ""
    print_info "批量执行配置:"
    echo "  Pod ID: ${POD_ID}"
    echo "  Product ID: ${PRODUCT_ID}"
    echo "  CSV 文件: ${CSV_FILE}"
    echo "  起始索引: ${START_INDEX}"
    echo "  结束索引: ${END_INDEX:-全部}"
    echo "  指令延迟: ${DELAY}秒"
    echo "  超时时间: ${TIMEOUT}秒"
    echo "  服务地址: ${AGENT_URL}"
    echo ""
}

# 确认执行
confirm_execution() {
    read -p "确认开始批量执行? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        print_info "用户取消执行"
        exit 0
    fi
}

# 执行批量任务
run_batch() {
    print_info "开始批量执行..."
    
    # 构建命令
    CMD="python3 batch_runner.py"
    CMD="${CMD} --pod-id ${POD_ID}"
    CMD="${CMD} --product-id ${PRODUCT_ID}"
    CMD="${CMD} --csv ${CSV_FILE}"
    CMD="${CMD} --start ${START_INDEX}"
    CMD="${CMD} --delay ${DELAY}"
    CMD="${CMD} --timeout ${TIMEOUT}"
    CMD="${CMD} --agent-url ${AGENT_URL}"
    
    if [ -n "${END_INDEX}" ]; then
        CMD="${CMD} --end ${END_INDEX}"
    fi
    
    print_info "执行命令: ${CMD}"
    echo ""
    
    # 执行命令
    if eval "${CMD}"; then
        print_success "批量执行完成!"
    else
        print_error "批量执行失败"
        exit 1
    fi
}

# 默认参数
POD_ID=""
PRODUCT_ID=""
START_INDEX=1
END_INDEX=""
CSV_FILE="cagui.csv"
DELAY=2.0
TIMEOUT=120
AGENT_URL="http://localhost:8000"

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        --pod-id)
            POD_ID="$2"
            shift 2
            ;;
        --product-id)
            PRODUCT_ID="$2"
            shift 2
            ;;
        --start)
            START_INDEX="$2"
            shift 2
            ;;
        --end)
            END_INDEX="$2"
            shift 2
            ;;
        --csv)
            CSV_FILE="$2"
            shift 2
            ;;
        --delay)
            DELAY="$2"
            shift 2
            ;;
        --timeout)
            TIMEOUT="$2"
            shift 2
            ;;
        --agent-url)
            AGENT_URL="$2"
            shift 2
            ;;
        --help)
            show_usage
            exit 0
            ;;
        *)
            print_error "未知参数: $1"
            show_usage
            exit 1
            ;;
    esac
done

# 检查必需参数
if [ -z "${POD_ID}" ] || [ -z "${PRODUCT_ID}" ]; then
    print_error "缺少必需参数: --pod-id 和 --product-id"
    echo ""
    show_usage
    exit 1
fi

# 主执行流程
main() {
    print_info "Mobile Agent 批量执行器启动"
    
    check_dependencies
    check_service
    show_execution_info
    confirm_execution
    run_batch
    
    print_success "所有任务完成!"
}

# 捕获中断信号
trap 'print_warning "用户中断执行"; exit 130' INT

# 执行主函数
main
