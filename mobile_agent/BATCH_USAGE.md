# Mobile Agent 批量执行使用指南

## 概述

这个批量执行脚本可以让您自动执行 CSV 文件中的多个指令，无需手动逐个操作。

## 前置条件

1. **确保服务已启动**：
   ```bash
   # 在 mobile_agent 目录下
   uv run main.py
   ```

2. **准备云手机资源**：
   - 需要有效的 `pod_id` (云手机实例ID)
   - 需要有效的 `product_id` (产品ID)

3. **CSV 文件**：
   - 默认使用 `cagui.csv` 文件
   - 包含 `id`, `instruction` 等列

## 基本使用

### 1. 执行所有指令
```bash
cd mobile_agent
python batch_runner.py --pod-id YOUR_POD_ID --product-id YOUR_PRODUCT_ID
```

### 2. 执行指定范围的指令
```bash
# 执行第1-10条指令
python batch_runner.py --pod-id YOUR_POD_ID --product-id YOUR_PRODUCT_ID --start 1 --end 10

# 从第5条开始执行到结束
python batch_runner.py --pod-id YOUR_POD_ID --product-id YOUR_PRODUCT_ID --start 5
```

### 3. 使用自定义 CSV 文件
```bash
python batch_runner.py --csv my_instructions.csv --pod-id YOUR_POD_ID --product-id YOUR_PRODUCT_ID
```

## 参数说明

| 参数 | 必需 | 默认值 | 说明 |
|------|------|--------|------|
| `--pod-id` | ✅ | - | 云手机实例ID |
| `--product-id` | ✅ | - | 产品ID |
| `--csv` | ❌ | cagui.csv | CSV文件路径 |
| `--start` | ❌ | 1 | 起始指令索引 |
| `--end` | ❌ | - | 结束指令索引(不指定则到末尾) |
| `--agent-url` | ❌ | http://localhost:8000 | Agent服务地址 |
| `--timeout` | ❌ | 120 | 单个指令超时时间(秒) |
| `--delay` | ❌ | 2.0 | 指令间延迟时间(秒) |

## 获取云手机信息

如果您不知道 `pod_id` 和 `product_id`，可以通过以下方式获取：

### 方法1: 查看配置文件
检查您的环境变量或配置文件中的云手机配置。

### 方法2: 通过 Web 界面
1. 启动 Web 服务：`cd web && npm run dev`
2. 访问 `http://localhost:8080?token=123`
3. 创建会话时会显示相关信息

### 方法3: 查看日志
启动 mobile agent 服务时，日志中会显示相关配置信息。

## 执行示例

### 示例1: 测试前3条指令
```bash
python batch_runner.py \
  --pod-id "your-pod-id-here" \
  --product-id "your-product-id-here" \
  --start 1 \
  --end 3 \
  --delay 3.0
```

### 示例2: 执行QQ音乐相关指令
```bash
# 根据 CSV 内容，前14条都是QQ音乐相关
python batch_runner.py \
  --pod-id "your-pod-id-here" \
  --product-id "your-product-id-here" \
  --start 1 \
  --end 14
```

## 输出结果

### 控制台输出
脚本会实时显示执行进度：
```
2024-01-10 10:00:00 - INFO - 会话创建成功: abc123
2024-01-10 10:00:01 - INFO - 执行指令 1: QQ 音乐播放成都
2024-01-10 10:00:15 - INFO - 指令 1 执行成功，耗时: 14.23s
2024-01-10 10:00:18 - INFO - 等待 2.0 秒...
...
```

### 结果文件
执行完成后会在 `batch_results/` 目录下生成 JSON 结果文件：
```json
[
  {
    "id": 1,
    "instruction": "QQ 音乐播放成都",
    "status": "success",
    "start_time": "2024-01-10T10:00:01",
    "duration": 14.23,
    "expected_result": "完成了\"QQ 音乐播放成都\"这个任务",
    "difficulty": "中等",
    "category": "娱乐——QQ音乐"
  }
]
```

### 执行摘要
```
==================================================
批量执行完成!
总指令数: 10
成功: 8
失败: 2
成功率: 80.0%
总耗时: 156.78s
平均耗时: 15.68s
==================================================
```

## 注意事项

1. **确保服务运行**：执行前确保 `uv run main.py` 正在运行
2. **云手机状态**：确保云手机实例处于可用状态
3. **网络连接**：确保网络连接稳定
4. **指令间隔**：建议保持适当的延迟避免过快执行
5. **监控执行**：可以随时按 `Ctrl+C` 中断执行

## 故障排除

### 常见错误

1. **连接拒绝**：
   ```
   Connection refused
   ```
   - 检查 mobile agent 服务是否启动
   - 检查端口是否正确 (默认8000)

2. **会话创建失败**：
   ```
   创建会话失败
   ```
   - 检查 pod_id 和 product_id 是否正确
   - 检查云手机实例是否可用

3. **指令执行超时**：
   ```
   timeout
   ```
   - 增加 `--timeout` 参数值
   - 检查云手机响应是否正常

### 调试模式
如需更详细的日志，可以修改脚本开头的日志级别：
```python
logging.basicConfig(level=logging.DEBUG)
```

## 高级用法

### 自定义 CSV 格式
脚本支持标准的 CSV 格式，必需列：
- `id`: 指令ID
- `instruction`: 要执行的指令

可选列：
- `expected_result`: 预期结果
- `步骤分级`: 难度级别
- `应用分类`: 应用类别

### 并发执行
当前版本为顺序执行，如需并发执行可以修改脚本或使用完整版批量执行器。
