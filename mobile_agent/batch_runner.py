#!/usr/bin/env python3
"""
批量执行
"""

import asyncio
import csv
import json
import logging
import time
import argparse
from pathlib import Path
from typing import List, Dict, Any
import httpx
import os
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


class SimpleBatchRunner:
    """执行器"""
    
    def __init__(self, 
                 agent_base_url: str = "http://localhost:8000",
                 pod_id: str = "7507161356387801883",
                 product_id: str = "1925071050769436672",
                 timeout: int = 120,
                 delay: float = 2.0):
        self.agent_base_url = agent_base_url.rstrip('/')
        self.pod_id = pod_id
        self.product_id = product_id
        self.timeout = timeout
        self.delay = delay
        self.session_id = None
        self.results = []
        
    async def create_session(self) -> str:
        """创建会话"""
        if not self.pod_id or not self.product_id:
            raise ValueError("pod_id 和 product_id 是必需的")

        url = f"{self.agent_base_url}/mobile-use/api/v1/session/create"
        data = {
            "pod_id": self.pod_id,
            "product_id": self.product_id
        }

        # 添加必要的请求头
        headers = {
            "X-Account-Id": "**********",  
            "Content-Type": "application/json"
        }

        async with httpx.AsyncClient(timeout=30) as client:
            response = await client.post(url, json=data, headers=headers)
            response.raise_for_status()
            result = response.json()
            
        if "thread_id" in result:
            # 直接返回格式
            self.session_id = result["thread_id"]
            logger.info(f"会话创建成功: {self.session_id}")
            return self.session_id
        elif result.get("code") == 200 and "result" in result:
            # 包装格式
            self.session_id = result["result"]["thread_id"]
            logger.info(f"会话创建成功: {self.session_id}")
            return self.session_id
        else:
            raise Exception(f"创建会话失败: {result}")
    
    async def execute_instruction(self, instruction: str, instruction_id: int) -> Dict[str, Any]:
        """执行单个指令"""
        if not self.session_id:
            raise ValueError("请先创建会话")
            
        url = f"{self.agent_base_url}/mobile-use/api/v1/agent/stream"
        data = {
            "message": instruction,
            "thread_id": self.session_id,
            "is_stream": True  
        }
        
        start_time = time.time()
        result = {
            "id": instruction_id,
            "instruction": instruction,
            "status": "pending",
            "start_time": datetime.now().isoformat(),
            "duration": 0,
            "cost": 0.0,
            "error": None,
            "response": None
        }
        
        try:
            logger.info(f"执行指令 {instruction_id}: {instruction}")

            # 添加必要的请求头
            headers = {
                "X-Account-Id": "**********",  # 批量执行使用的测试账户ID
                "Content-Type": "application/json"
            }

            async with httpx.AsyncClient(timeout=self.timeout) as client:
                # 使用流式请求处理 SSE 响应
                async with client.stream("POST", url, json=data, headers=headers) as response:
                    response.raise_for_status()

                    # 收集所有 SSE 数据
                    content_parts = []
                    async for chunk in response.aiter_text():
                        if chunk.strip():
                            content_parts.append(chunk.strip())

                    content = "\n".join(content_parts)
                    result["response"] = content
                    result["status"] = "success"

                # 提取成本信息 TODO: 需要根据实际情况修改
                cost = self.extract_cost_from_response(content)
                result["cost"] = cost

                duration = time.time() - start_time
                result["duration"] = round(duration, 2)

                logger.info(f"指令 {instruction_id} 执行成功，耗时: {duration:.2f}s，成本: ¥{cost:.4f}")
                
        except Exception as e:
            duration = time.time() - start_time
            result["duration"] = round(duration, 2)
            result["status"] = "failed"
            result["error"] = str(e)
            result["cost"] = 0.0
            logger.error(f"指令 {instruction_id} 执行失败: {e}")
            
        return result
    
    def load_csv(self, csv_path: str, start_index: int = 1, end_index: int = None) -> List[Dict[str, Any]]:
        """加载 CSV 文件"""
        instructions = []
        
        with open(csv_path, 'r', encoding='utf-8') as file:
            reader = csv.DictReader(file)
            reader.fieldnames = [fn.lstrip('\ufeff') for fn in reader.fieldnames]
            for row in reader:
                try:
                    instruction_id = int(row['id'])
                    if instruction_id < start_index:
                        continue
                    if end_index and instruction_id > end_index:
                        break
                        
                    instructions.append({
                        'id': instruction_id,
                        'instruction': row['instruction'],
                        'expected_result': row.get('expected_result', ''),
                        'difficulty': row.get('步骤分级', ''),
                        'category': row.get('应用分类', '')
                    })
                except (ValueError, KeyError) as e:
                    logger.warning(f"跳过无效行: {row}, 错误: {e}")
                    
        logger.info(f"加载了 {len(instructions)} 条指令")
        return instructions
    

    async def return_to_home_and_exit_apps(self):
        """返回主页面并完全退出所有应用"""
        logger.info("正在返回主页面并退出所有应用...")

        if not self.session_id:
            logger.warning("会话未创建，跳过返回主页面操作")
            return

        url = f"{self.agent_base_url}/mobile-use/api/v1/agent/stream"
        data = {
            "message": "返回主页面并完全退出所有应用",
            "thread_id": self.session_id,
            "is_stream": True
        }

        headers = {
            "X-Account-Id": "**********",
            "Content-Type": "application/json"
        }

        try:
            async with httpx.AsyncClient(timeout=30) as client:
                async with client.stream("POST", url, json=data, headers=headers) as response:
                    response.raise_for_status()

                    # 等待操作完成
                    async for chunk in response.aiter_text():
                        if "finished" in chunk.lower():
                            break

            logger.info("返回主页面操作完成")

        except Exception as e:
            logger.warning(f"返回主页面操作失败: {e}")

    def extract_cost_from_response(self, response_text: str) -> float:
        """从响应中提取成本信息"""
        if not response_text:
            return 0.0

        try:
            # 查找成本相关的信息
            import re

            # 匹配各种成本格式
            cost_patterns = [
                r'总成本[：:]\s*([0-9.]+)',
                r'cost[：:]\s*([0-9.]+)',
                r'费用[：:]\s*([0-9.]+)',
                r'消耗[：:]\s*([0-9.]+)',
                r'¥([0-9.]+)',
                r'\$([0-9.]+)'
            ]

            for pattern in cost_patterns:
                match = re.search(pattern, response_text, re.IGNORECASE)
                if match:
                    return float(match.group(1))

            return 0.0

        except Exception as e:
            logger.warning(f"提取成本信息失败: {e}")
            return 0.0
    async def run_batch(self, csv_path: str, start_index: int = 1, end_index: int = None):
        """运行批量执行"""
        logger.info("开始批量执行...")
        
        # 加载指令
        instructions = self.load_csv(csv_path, start_index, end_index)
        if not instructions:
            logger.error("没有找到要执行的指令")
            return
        
        # 创建会话
        await self.create_session()
        
        # 执行指令
        for i, instruction_data in enumerate(instructions):
            try:
                # 执行指令
                result = await self.execute_instruction(
                    instruction_data['instruction'],
                    instruction_data['id']
                )
                result.update({
                    'expected_result': instruction_data['expected_result'],
                    'difficulty': instruction_data['difficulty'],
                    'category': instruction_data['category']
                })
                self.results.append(result)

                if i < len(instructions) - 1:
                    logger.info("准备执行下一个指令，先返回主页面...")
                    await self.return_to_home_and_exit_apps()

                    # 延迟
                    logger.info(f"等待 {self.delay} 秒...")
                    await asyncio.sleep(self.delay)

            except Exception as e:
                logger.error(f"执行指令时发生错误: {e}")
                # 即使出错也要尝试返回主页面
                if i < len(instructions) - 1:
                    try:
                        await self.return_to_home_and_exit_apps()
                        await asyncio.sleep(self.delay)
                    except:
                        pass
                continue
        
        # 保存结果
        self.save_results()
        self.print_summary()
    
    def save_results(self):
        """保存执行结果"""
        output_dir = Path("batch_results")
        output_dir.mkdir(exist_ok=True)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_file = output_dir / f"batch_results_{timestamp}.json"
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(self.results, f, ensure_ascii=False, indent=2)
            
        logger.info(f"结果已保存到: {output_file}")
    
    def print_summary(self):
        """打印执行摘要"""
        total = len(self.results)
        success = len([r for r in self.results if r['status'] == 'success'])
        failed = total - success

        total_duration = sum(r['duration'] for r in self.results)
        avg_duration = total_duration / total if total > 0 else 0

        total_cost = sum(r.get('cost', 0.0) for r in self.results)
        avg_cost = total_cost / total if total > 0 else 0

        logger.info("=" * 60)
        logger.info("批量执行完成!")
        logger.info(f"总指令数: {total}")
        logger.info(f"成功: {success}")
        logger.info(f"失败: {failed}")
        logger.info(f"成功率: {success/total*100:.1f}%")
        logger.info(f"总耗时: {total_duration:.2f}s")
        logger.info(f"平均耗时: {avg_duration:.2f}s")
        logger.info(f"总成本: ¥{total_cost:.4f}")
        logger.info(f"平均成本: ¥{avg_cost:.4f}")
        logger.info("=" * 60)

        # 打印详细结果
        logger.info("\n详细执行结果:")
        logger.info("-" * 60)
        for result in self.results:
            status_icon = "✅" if result['status'] == 'success' else "❌"
            logger.info(f"{status_icon} 指令 {result['id']}: {result['instruction'][:30]}...")
            logger.info(f"   状态: {result['status']}")
            logger.info(f"   耗时: {result['duration']:.2f}s")
            logger.info(f"   成本: ¥{result.get('cost', 0.0):.4f}")
            if result.get('error'):
                logger.info(f"   错误: {result['error']}")
            logger.info("")


def main():
    parser = argparse.ArgumentParser(description="Mobile Agent 批量执行器")
    parser.add_argument("--csv", default="cagui.csv", help="CSV 文件路径")
    parser.add_argument("--start", type=int, default=1, help="起始索引")
    parser.add_argument("--end", type=int, help="结束索引")
    parser.add_argument("--pod-id", default="7507161356387801883", help="云手机 Pod ID")
    parser.add_argument("--product-id", default="1925071050769436672", help="产品 ID")
    parser.add_argument("--agent-url", default="http://localhost:8000", help="Agent 服务地址")
    parser.add_argument("--timeout", type=int, default=120, help="单个指令超时时间(秒)")
    parser.add_argument("--delay", type=float, default=2.0, help="指令间延迟(秒)")
    
    args = parser.parse_args()
    
    # 检查 CSV 文件
    if not Path(args.csv).exists():
        logger.error(f"CSV 文件不存在: {args.csv}")
        return
    
    # 创建批量执行器
    runner = SimpleBatchRunner(
        agent_base_url=args.agent_url,
        pod_id=args.pod_id,
        product_id=args.product_id,
        timeout=args.timeout,
        delay=args.delay
    )
    
    # 运行批量执行
    try:
        asyncio.run(runner.run_batch(args.csv, args.start, args.end))
    except KeyboardInterrupt:
        logger.info("用户中断执行")
    except Exception as e:
        logger.error(f"批量执行失败: {e}")


if __name__ == "__main__":
    main()
