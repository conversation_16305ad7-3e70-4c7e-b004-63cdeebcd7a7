#!/usr/bin/env python3
"""
测试 Mobile Agent 连接和配置的脚本
"""

import asyncio
import httpx
import json
import sys
from pathlib import Path


async def test_agent_service(base_url="http://localhost:8000"):
    """测试 Agent 服务连接"""
    print(f"🔍 测试 Agent 服务连接: {base_url}")
    
    try:
        async with httpx.AsyncClient(timeout=10) as client:
            # 测试健康检查
            response = await client.get(f"{base_url}/docs")
            if response.status_code == 200:
                print("✅ Agent 服务运行正常")
                return True
            else:
                print(f"❌ Agent 服务响应异常: {response.status_code}")
                return False
    except Exception as e:
        print(f"❌ 无法连接到 Agent 服务: {e}")
        print("💡 请确保已启动服务: uv run main.py")
        return False


async def test_session_creation(base_url, pod_id, product_id):
    """测试会话创建"""
    print(f"🔍 测试会话创建...")
    print(f"   Pod ID: {pod_id}")
    print(f"   Product ID: {product_id}")
    
    url = f"{base_url}/mobile-use/api/v1/session/create"
    data = {
        "pod_id": pod_id,
        "product_id": product_id
    }
    
    try:
        async with httpx.AsyncClient(timeout=30) as client:
            response = await client.post(url, json=data)
            
            if response.status_code == 200:
                result = response.json()
                if result.get("code") == 200:
                    thread_id = result["result"]["thread_id"]
                    print(f"✅ 会话创建成功: {thread_id}")
                    return thread_id
                else:
                    print(f"❌ 会话创建失败: {result}")
                    return None
            else:
                print(f"❌ HTTP 错误: {response.status_code}")
                print(f"   响应: {response.text}")
                return None
                
    except Exception as e:
        print(f"❌ 会话创建异常: {e}")
        return None


async def test_simple_instruction(base_url, thread_id):
    """测试简单指令执行"""
    print(f"🔍 测试简单指令执行...")
    
    url = f"{base_url}/mobile-use/api/v1/agent/stream"
    data = {
        "message": "截图",  # 简单的截图指令
        "thread_id": thread_id,
        "is_stream": False
    }
    
    try:
        async with httpx.AsyncClient(timeout=60) as client:
            response = await client.post(url, json=data)
            
            if response.status_code == 200:
                print("✅ 指令执行成功")
                print(f"   响应长度: {len(response.text)} 字符")
                return True
            else:
                print(f"❌ 指令执行失败: {response.status_code}")
                print(f"   响应: {response.text[:200]}...")
                return False
                
    except Exception as e:
        print(f"❌ 指令执行异常: {e}")
        return False


def check_csv_file(csv_path="cagui.csv"):
    """检查 CSV 文件"""
    print(f"🔍 检查 CSV 文件: {csv_path}")
    
    if not Path(csv_path).exists():
        print(f"❌ CSV 文件不存在: {csv_path}")
        return False
    
    try:
        import csv
        with open(csv_path, 'r', encoding='utf-8') as file:
            reader = csv.DictReader(file)
            rows = list(reader)
            
        print(f"✅ CSV 文件读取成功")
        print(f"   总指令数: {len(rows)}")
        
        if rows:
            first_row = rows[0]
            print(f"   第一条指令: {first_row.get('instruction', 'N/A')}")
            
        return True
        
    except Exception as e:
        print(f"❌ CSV 文件读取失败: {e}")
        return False


def print_usage_examples():
    """打印使用示例"""
    print("\n📖 使用示例:")
    print("1. 基本用法:")
    print("   python batch_runner.py --pod-id YOUR_POD_ID --product-id YOUR_PRODUCT_ID")
    print()
    print("2. 执行前3条指令:")
    print("   python batch_runner.py --pod-id YOUR_POD_ID --product-id YOUR_PRODUCT_ID --start 1 --end 3")
    print()
    print("3. 使用快速启动脚本:")
    print("   ./run_batch.sh --pod-id YOUR_POD_ID --product-id YOUR_PRODUCT_ID --start 1 --end 3")
    print()
    print("4. 使用环境变量:")
    print("   cp .env.batch.example .env.batch")
    print("   # 编辑 .env.batch 文件")
    print("   python batch_with_env.py")


async def main():
    print("🚀 Mobile Agent 批量执行连接测试")
    print("=" * 50)
    
    # 获取参数
    if len(sys.argv) < 3:
        print("❌ 缺少参数")
        print("用法: python test_connection.py POD_ID PRODUCT_ID [AGENT_URL]")
        print("示例: python test_connection.py abc123 def456")
        print_usage_examples()
        sys.exit(1)
    
    pod_id = sys.argv[1]
    product_id = sys.argv[2]
    agent_url = sys.argv[3] if len(sys.argv) > 3 else "http://localhost:8000"
    
    print(f"配置信息:")
    print(f"  Pod ID: {pod_id}")
    print(f"  Product ID: {product_id}")
    print(f"  Agent URL: {agent_url}")
    print()
    
    # 测试步骤
    success_count = 0
    total_tests = 4
    
    # 1. 检查 CSV 文件
    if check_csv_file():
        success_count += 1
    print()
    
    # 2. 测试 Agent 服务
    if await test_agent_service(agent_url):
        success_count += 1
    print()
    
    # 3. 测试会话创建
    thread_id = await test_session_creation(agent_url, pod_id, product_id)
    if thread_id:
        success_count += 1
    print()
    
    # 4. 测试指令执行
    if thread_id and await test_simple_instruction(agent_url, thread_id):
        success_count += 1
    print()
    
    # 总结
    print("=" * 50)
    print(f"测试完成: {success_count}/{total_tests} 项通过")
    
    if success_count == total_tests:
        print("🎉 所有测试通过！可以开始批量执行")
        print_usage_examples()
    else:
        print("⚠️  部分测试失败，请检查配置")
        
        if success_count < 2:
            print("\n💡 建议:")
            print("1. 确保 Mobile Agent 服务已启动: uv run main.py")
            print("2. 检查 pod_id 和 product_id 是否正确")
            print("3. 确保云手机实例处于可用状态")


if __name__ == "__main__":
    asyncio.run(main())
