# Mobile-Use 执行流程详细分析

## 系统概述

Mobile-Use 是一个基于三层架构的 AI Agent 系统，能够通过自然语言指令在云手机环境中执行自动化任务。

## 核心组件架构

### 1. Web Frontend Layer (Next.js)
- **职责**: 用户交互界面，实时反馈显示
- **技术栈**: React/Next.js + SSE (Server-Sent Events)
- **关键功能**: 
  - 任务输入和会话管理
  - 实时流式显示 AI 推理过程
  - 操作结果可视化

### 2. Mobile Agent Layer (Python)
- **职责**: 核心 AI 推理和任务编排
- **技术栈**: FastAPI + LangGraph + LangChain
- **关键组件**:

#### 2.1 会话管理 (Session Manager)
```python
class SessionState:
    account_id: str
    chat_thread_id: str          # LangGraph 上下文 ID
    pod_id: str                  # 云手机实例 ID
    authorization_token: str     # MCP 授权令牌
    pod_size: PodSize           # 屏幕尺寸信息
    sse_connection: asyncio.Event # 实时连接控制
```

#### 2.2 LangGraph 状态机
```python
# 状态机节点定义
workflow.add_node("prepare", prepare_node)      # 初始化
workflow.add_node("model", model_node)          # AI 推理
workflow.add_node("tool_valid", tool_valid_node) # 工具验证
workflow.add_node("tool", tool_node)            # 工具执行

# 条件边控制流程
workflow.add_conditional_edges(
    "tool_valid",
    should_tool_exec_continue,
    {"continue": "tool", "retry": "model"}
)
```

### 3. MCP Server Layer (Go)
- **职责**: 云手机操作工具层
- **技术栈**: Go + MCP Protocol
- **支持工具**:

| 工具名称 | 功能描述 | 参数 |
|---------|---------|------|
| `take_screenshot` | 获取屏幕截图 | 无 |
| `tap` | 点击指定坐标 | x, y |
| `swipe` | 滑动手势 | fromX, fromY, toX, toY |
| `text_input` | 文本输入 | text |
| `autoinstall_app` | 自动安装应用 | download_url, app_name |
| `launch_app` | 启动应用 | package_name |
| `close_app` | 关闭应用 | package_name |
| `list_apps` | 列出已安装应用 | 无 |
| `home/back/menu` | 系统按键 | 无 |

## 详细执行流程

### 阶段 1: 会话创建和初始化

1. **用户访问**: 用户通过 Web 界面访问系统
2. **会话创建**: 
   ```http
   POST /api/v1/session/create
   {
     "product_id": "云手机产品ID",
     "pod_id": "云手机实例ID"
   }
   ```
3. **资源验证**: Session Manager 向 ACEP 验证云手机资源
4. **授权获取**: 获取 MCP 服务器访问授权和 TOS 存储凭证
5. **会话建立**: 返回 `thread_id` 给前端

### 阶段 2: 任务执行

1. **任务提交**:
   ```http
   POST /api/v1/agent/stream
   {
     "message": "用户任务指令",
     "thread_id": "会话ID",
     "is_stream": true
   }
   ```

2. **Agent 初始化**:
   ```python
   agent = MobileUseAgent()
   await agent.initialize(
       pod_id=session.pod_id,
       auth_token=session.authorization_token,
       product_id=session.product_id,
       tos_bucket=session.tos_bucket,
       tos_region=session.tos_region,
       tos_endpoint=session.tos_endpoint,
   )
   ```

3. **LangGraph 工作流启动**:
   ```python
   async for chunk in graph.astream(
       input={
           "user_prompt": query,
           "iteration_count": 0,
           "task_id": self.task_id,
           "thread_id": thread_id,
           "max_iterations": self.max_steps,
       },
       config={"configurable": {"thread_id": thread_id}},
       stream_mode=["messages", "custom"],
   ):
       yield chunk  # 实时流式输出
   ```

### 阶段 3: LangGraph 状态机执行

#### 3.1 Prepare Node (准备节点)
- 初始化上下文管理器
- 添加系统提示词
- 设置消息历史

#### 3.2 Model Node (AI 推理节点)
```python
async def model_node(state: MobileUseAgentState):
    # 1. 获取当前屏幕截图
    screenshot_state = await mobile.take_screenshot()
    
    # 2. 调用豆包视觉大模型
    llm = DoubaoLLM(thread_id=state.get("thread_id"))
    chunk_id, content, summary, tool_call = await llm.async_chat(
        context_manager.get_messages()
    )
    
    # 3. 更新状态
    state.update(
        tool_call_str=tool_call,
        iteration_count=iteration_count + 1,
        messages=context_manager.get_messages(),
    )
    return state
```

#### 3.3 Tool Valid Node (工具验证节点)
```python
async def tool_valid_node(state: MobileUseAgentState):
    tool_call_str = state.get("tool_call_str")
    action_parser = agent_object_manager.get_action_parser(state.get("thread_id"))
    
    # 解析工具调用字符串为 MCP 格式
    tool_call = action_parser.to_mcp_tool_call(tool_call_str)
    state.update(tool_call=tool_call)
    return state
```

#### 3.4 Tool Node (工具执行节点)
```python
async def tool_node(state: MobileUseAgentState):
    tool_call = state.get("tool_call")
    tools = agent_object_manager.get_tools(state.get("thread_id"))
    
    try:
        # 通过 MCP 协议执行工具
        result = await tools.exec(tool_call)
        output = {"result": f"{tool_name}:({tool_call.get('arguments', {})})\n{result}\n操作下发成功"}
        state.update(tool_output=output)
    except Exception as e:
        output = {"result": f"Error: {str(e)}"}
        state.update(tool_output=output)
    
    return state
```

### 阶段 4: MCP 协议工具调用

1. **MCP 客户端调用**:
   ```python
   response = await session.call_tool(name, arguments)
   ```

2. **MCP 服务器处理**:
   ```go
   func HandleTakeScreenshot() mcp.ToolHandler {
       return func(ctx context.Context, args map[string]interface{}) (*mcp.CallToolResult, error) {
           handler, err := GetMobileUseHandler(ctx)
           result, err := handler.ScreenShot(ctx)
           return mcp.NewToolResult(mcp.NewTextContent(jsonResult)), nil
       }
   }
   ```

3. **ACEP 云手机操作**:
   ```go
   func (impl *mobileUseImpl) ScreenShot(ctx context.Context) (*ScreenShotResult, error) {
       request := &acep.GetScreenshotBody{
           ProductID: impl.option.ProductID,
           PodIDList: []string{impl.option.DeviceID},
       }
       response, err := impl.acep.GetScreenshot(ctx, request)
       // 上传截图到 TOS 存储
       return &ScreenShotResult{
           ScreenshotURL: uploadedURL,
           Width: width,
           Height: height,
       }, nil
   }
   ```

## 实时反馈机制

### SSE (Server-Sent Events) 流式输出
```python
# AI 推理过程反馈
sse_writer(get_writer_think(state, chunk_id, summary))

# 工具执行反馈
sse_writer(get_writer_tool_input(state, tool_call))
sse_writer(get_writer_tool_output(state, tool_call, output))
```

### 前端实时显示
```javascript
const eventSource = new EventSource('/api/v1/agent/stream');
eventSource.onmessage = (event) => {
    const data = JSON.parse(event.data);
    // 根据消息类型更新 UI
    updateUI(data.type, data.content);
};
```

## 错误处理和重试机制

1. **工具验证失败**: 重新调用 Model Node 生成新的动作
2. **工具执行失败**: 记录错误信息，继续下一轮推理
3. **会话超时**: 自动清理资源，通知用户重新开始
4. **用户取消**: 通过 SSE 连接中断机制立即停止执行

## 性能优化特性

1. **图片历史管理**: 只保留最近 5 张截图，减少内存占用
2. **异步执行**: 全流程异步处理，提高并发性能
3. **连接池**: MCP 客户端复用连接，减少建立开销
4. **懒加载清理**: 定期清理过期会话，优化内存使用

## 安全机制

1. **授权验证**: 每个请求都需要有效的授权令牌
2. **会话隔离**: 不同用户的会话完全隔离
3. **资源限制**: 限制最大执行步数和会话时长
4. **错误隔离**: 异常不会影响其他会话的执行
