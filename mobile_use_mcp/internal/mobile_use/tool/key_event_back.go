package tool

import (
	"context"
	"math"
	"mcp_server_mobile_use/internal/mobile_use/consts"

	"github.com/mark3labs/mcp-go/mcp"
)

func NewKeyEventBackTool() mcp.Tool {
	return mcp.NewTool("back",
		mcp.WithDescription("Go one step/page back on the cloud phone"),
		mcp.WithNumber("count",
			mcp.Description("The number of times to press the back key, default is 1, min is 1, max is 3"),
		),
	)
}

func ClampCount(value, min, max int) int {
	return int(math.Min(math.Max(float64(value), float64(min)), float64(max)))
}

func HandleKeyEventBackTool() func(context.Context, mcp.CallToolRequest) (*mcp.CallToolResult, error) {
	return func(ctx context.Context, req mcp.CallToolRequest) (*mcp.CallToolResult, error) {
		err := CheckAuth(ctx)
		if err != nil {
			return CallResultError(err)
		}
		mobileUseConfig, err := GetMobileUseConfig(ctx)
		if err != nil || mobileUseConfig == nil {
			return CallResultError(err)
		}
		handler, err := InitMobileUseService(ctx, mobileUseConfig)
		if err != nil {
			return CallResultError(err)
		}
		args, err := CheckArgs(req.Params.Arguments)
		if err != nil {
			return CallResultError(err)
		}
		count := 1
		if countArg, ok := args["count"]; ok {
			if countVal, ok := countArg.(float64); ok {
				count = int(countVal)
			} else if countVal, ok := countArg.(int); ok {
				count = countVal
			}
		}

		for i := 0; i < ClampCount(count, 1, 3); i++ {
			err = handler.KeyEvent(ctx, consts.AndroidKeyEventBack)
			if err != nil {
				return CallResultError(err)
			}
		}

		return CallResultSuccess("Back successfully")
	}
}
