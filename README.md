# Mobile Use - Solution for Mobile AI Infra to Agent 

English | [简体中文](README_zh.md)


## 🚀 Overview

[Mobile Use Product Documentation](https://www.volcengine.com/docs/6394/1583515)


**Mobile Use** is an AI Agent solution based on **Volcano Engine Cloud Phone** and **Doubao Visual Large Model** capabilities, which is able to complete automated tasks for mobile scenarios through natural language instructions.

Currently, Mobile Use has been officially launched on [veFaaS Application Center](https://console.volcengine.com/vefaas/region:vefaas+cn-beijing/market). Click to experience our Mobile Use Agent demo online through the link. If you want to develop your own Mobile Use Agent, go to [one-click deployment](https://console.volcengine.com/vefaas/region:vefaas+cn-beijing/application/create) to quickly complete the deployment process and start a journey of integrating Mobile Use Agent into your business flow.


## ✨ Features

- **AI-Powered Automation**: Accurately identify, understand and click on mobile applications and complex scenarios based on Doubao visual large model
- **Cloud Phone Integration**: Execute automated tasks in a secure, stable and low-latency Cloud Phone isolated environment
- **MCP Protocol Support**: Standard Model Context Protocol (MCP) Tool: [Mobile Use MCP](https://github.com/volcengine/mcp-server/tree/main/server/mcp_server_mobile_use)
- **Web Interface**: Modern React/Next.js web interface for interaction and monitoring
- **Real-time Streaming**: SSE-based real-time communication and feedback
- **Extensible Architecture**: Modular design for easy extension and intregration into actual business flow

## 🏗️ Architecture

The project consists of three main components:

```
mobile-use/
├── mobile_agent/      # Python AI Agent Core
├── mobile_use_mcp/    # Go MCP Server
└── web/              # Next.js Web Frontend
```

### Core Components

1. **Mobile Agent** (Python)
   - AI reasoning and decision making
   - Vision model integration
   - Task orchestration and execution
   - Memory and context management

2. **MCP Server** (Go)
   - Cloud phone interaction layer
   - Standard MCP protocol implementation
   - Mobile automation tools and APIs

3. **Web Frontend** (Next.js)
   - User interface and interaction
   - Real-time monitoring and feedback
   - Task management and visualization

## 🛠️ Available Tools

| Tool | Description |
|------|-------------|
| `take_screenshot` | Capture cloud phone screen |
| `tap` | Tap at specified coordinates |
| `swipe` | Perform swipe gestures |
| `text_input` | Input text on screen |
| `home` | Go to home screen |
| `back` | Go back to previous screen |
| `menu` | Open menu |
| `autoinstall_app` | Auto-download and install apps |
| `launch_app` | Launch apps |
| `close_app` | Close apps |
| `list_apps` | List all installed apps |

## 🚦 Quick Start

### Prerequisites

- **Node.js** >= 20 (use [nvm](https://github.com/nvm-sh/nvm) for version management) 
- **Python** >= 3.11 (use [uv](https://docs.astral.sh/uv/) for dependency management)
- **Go** >= 1.23 (for MCP server) [install](https://go.dev/doc/install)
> [!NOTE]
> mobile_use_mcp currently only supports Linux systems build


### Installation

1. **Clone the repository**

```bash
git clone https://github.com/volcengine/ai-app-lab.git
cd demohouse/mobile-use
```

2. **Install dependencies**
```bash
sh setup.sh
```

3. **Configure environment**
```bash
# Copy and edit configuration files
cp mobile_agent/.env.example mobile_agent/.env
cp web/.env.example web/.env
# Edit configuration with your API keys and endpoints
```

### agent config
```bash
MOBILE_USE_MCP_URL= # MCP service URL http://xxxx.com/mcp, local url is http://localhost:8888/mcp

TOS_BUCKET= # Volcengine Object Storage bucket
TOS_REGION= # Volcengine Object Storage region
TOS_ENDPOINT= # Volcengine Object Storage endpoint

ARK_API_KEY= # Volcengine API key
ARK_MODEL_ID= # Volcengine model ID
ARK_BASE_URL= # Volengine ARK API base url

ACEP_AK= # Volcengine cloud phone AK
ACEP_SK= # Volcengine cloud phone SK
ACEP_ACCOUNT_ID= # Volcengine accountID
LANGSMITH_TRACING= # true or false
LANGSMITH_API_KEY= # Langsmith API key
LANGSMITH_PROJECT= # Langsmith project name
MOBILE_FORNAX_ENABLE=true  # true or false
MOBILE_FORNAX_AK="" # Fornax AK and SK
MOBILE_FORNAX_SK="" # Fornax AK and SK
BYTED_HOST_IPV6=1 #default
CONSUL_HTTP_HOST=************
CONSUL_HTTP_PORT=2280
RUNTIME_IDC_NAME=boe
FORNAX_CUSTOM_REGION=boecn
FORNAX_DEBUG_MODE=true
```

### web config

```bash
CLOUD_AGENT_BASE_URL= # agent service URL http://xxxx.com/mobile-use/
```

4. **Start services**

Start MCP server:
```bash
cd mobile_use_mcp
go run cmd/mobile_use_mcp/main.go  -t streamable-http -p 8888
```

Start the mobile agent service:
```bash
cd mobile_agent
uv venv
source .venv/bin/activate
uv pip install -e .
uv run main.py
```

Start web frontend:
```bash
cd web
npm run dev
```


5. **Access the application**

Open your browser and navigate to `http://localhost:8080?token=123`

## 🔧 Development

### Project Structure

```
mobile_agent/
├── mobile_agent/
│   ├── config/          # Configuration management
│   ├── service/
│   │   ├── agent/       # Core AI agent logic
│   │   ├── session/     # Session management
│   │   └── pod/         # Cloud phone management
│   ├── routers/         # FastAPI route handlers
│   └── middleware/      # Request middleware
└── main.py             # Application entry point
```

## 📚 API 

### REST Endpoints

- `POST /api/agent/stream` - Start AI agent task with streaming response
- `POST /api/agent/cancel` - Cancel running agent task
- `POST /api/session/create` - Create new session
- `POST /api/session/reset` - Reset session state

